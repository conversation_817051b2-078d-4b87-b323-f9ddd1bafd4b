// 检查修改后的文件语法
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/pages/activity/96012/record/components/DynamicRecordSetting.tsx');

try {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查基本的语法错误
  const issues = [];
  
  // 检查括号匹配
  const openBraces = (content.match(/\{/g) || []).length;
  const closeBraces = (content.match(/\}/g) || []).length;
  if (openBraces !== closeBraces) {
    issues.push(`括号不匹配: { ${openBraces} vs } ${closeBraces}`);
  }
  
  const openParens = (content.match(/\(/g) || []).length;
  const closeParens = (content.match(/\)/g) || []).length;
  if (openParens !== closeParens) {
    issues.push(`圆括号不匹配: ( ${openParens} vs ) ${closeParens}`);
  }
  
  // 检查是否正确导入了 Balloon 组件
  const hasBaloonImport = content.includes('Balloon');
  
  console.log('语法检查结果:');
  if (issues.length === 0) {
    console.log('✅ 没有发现明显的语法错误');
    console.log(`📊 统计信息:`);
    console.log(`   - 总行数: ${content.split('\n').length}`);
    console.log(`   - 大括号: ${openBraces} 对`);
    console.log(`   - 圆括号: ${openParens} 对`);
    console.log(`   - Balloon组件已导入: ${hasBaloonImport ? '✅' : '❌'}`);
  } else {
    console.log('❌ 发现以下问题:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
} catch (error) {
  console.error('❌ 读取文件失败:', error.message);
}
