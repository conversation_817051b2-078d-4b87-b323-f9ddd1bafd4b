import {
  Activity94008DataRequest,
  Activity94008DataResponse,
  Activity94008UpdateUserMessageRequest,
  Activity94008UserMessageRequest,
  IPageActivity94008UserMessageResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags oppo新机发布明星合拍数据业务
 * @summary 获取活动数据导出
 * @request POST:/94008/data/data/export
 */
export const dataDataExport = (request: Activity94008DataRequest): Promise<void> => {
  return httpRequest({
    url: '/94008/data/data/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags oppo新机发布明星合拍数据业务
 * @summary 获取活动数据
 * @request POST:/94008/data/getDataList
 */
export const dataGetDataList = (request: Activity94008DataRequest): Promise<Activity94008DataResponse[]> => {
  return httpRequest({
    url: '/94008/data/getDataList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags oppo新机发布明星合拍
 * @summary 获取94008活动用户消息列表
 * @request POST:/94008/getUserMessagePage
 */
export const getUserMessagePage = (
  request: Activity94008UserMessageRequest,
): Promise<IPageActivity94008UserMessageResponse> => {
  return httpRequest({
    url: '/94008/getUserMessagePage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags oppo新机发布明星合拍
 * @summary 修改94008活动用户消息精选状态
 * @request POST:/94008/updateSelectedFlag
 */
export const updateSelectedFlag = (request: Activity94008UpdateUserMessageRequest): Promise<void> => {
  return httpRequest({
    url: '/94008/updateSelectedFlag',
    method: 'post',
    data: request,
  });
};
