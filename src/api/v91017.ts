import {
  Activity91017CreateOrUpdateRequest,
  Activity91017CreateOrUpdateResponse,
  Activity91017DownloadTemplate,
  Activity91017MemberReportResponse,
  Activity91017ReportPageRequest,
  Activity91017ReportPageResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  CK04DwStageActivity91017PeriodData,
  CK04DwStageActivity91017TtlData,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 创建活动
 * @request POST:/91017/createActivity
 */
export const createActivity = (
  request: Activity91017CreateOrUpdateRequest,
): Promise<Activity91017CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/91017/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 会员数据报表
 * @request POST:/91017/data/memberReport
 */
export const dataMemberReport = (
  request: Activity91017ReportPageRequest,
): Promise<Activity91017MemberReportResponse[]> => {
  return httpRequest({
    url: '/91017/data/memberReport',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 会员数据报表导出
 * @request POST:/91017/data/memberReport/export
 */
export const dataMemberReportExport = (request: Activity91017ReportPageRequest): Promise<void> => {
  return httpRequest({
    url: '/91017/data/memberReport/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary ori数据报表
 * @request POST:/91017/data/oriReport
 */
export const dataOriReport = (request: Activity91017ReportPageRequest): Promise<Activity91017ReportPageResponse[]> => {
  return httpRequest({
    url: '/91017/data/oriReport',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary ori数据报表导出
 * @request POST:/91017/data/oriReport/export
 */
export const dataOriReportExport = (request: Activity91017ReportPageRequest): Promise<void> => {
  return httpRequest({
    url: '/91017/data/oriReport/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary period数据报表
 * @request POST:/91017/data/periodReport
 */
export const dataPeriodReport = (
  request: Activity91017ReportPageRequest,
): Promise<CK04DwStageActivity91017PeriodData[]> => {
  return httpRequest({
    url: '/91017/data/periodReport',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary period数据报表导出
 * @request POST:/91017/data/periodReport/export
 */
export const dataPeriodReportExport = (request: Activity91017ReportPageRequest): Promise<void> => {
  return httpRequest({
    url: '/91017/data/periodReport/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 数据报表
 * @request POST:/91017/data/report
 */
export const dataReport = (request: Activity91017ReportPageRequest): Promise<Activity91017ReportPageResponse[]> => {
  return httpRequest({
    url: '/91017/data/report',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 数据报表导出
 * @request POST:/91017/data/report/export
 */
export const dataReportExport = (request: Activity91017ReportPageRequest): Promise<void> => {
  return httpRequest({
    url: '/91017/data/report/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary ttl数据报表
 * @request POST:/91017/data/ttlReport
 */
export const dataTtlReport = (request: Activity91017ReportPageRequest): Promise<CK04DwStageActivity91017TtlData[]> => {
  return httpRequest({
    url: '/91017/data/ttlReport',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary ttl数据报表导出
 * @request POST:/91017/data/ttlReport/export
 */
export const dataTtlReportExport = (request: Activity91017ReportPageRequest): Promise<void> => {
  return httpRequest({
    url: '/91017/data/ttlReport/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 下载模板
 * @request POST:/91017/downloadTemplate/export
 */
export const downloadTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/91017/downloadTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 查询活动信息
 * @request POST:/91017/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/91017/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 导入模版信息excel
 * @request POST:/91017/importSeriesExcel
 */
export const importSeriesExcel = (file: any): Promise<Activity91017DownloadTemplate[]> => {
  return httpRequest({
    url: '/91017/importSeriesExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 惠氏集罐有礼-0元试喝
 * @summary 修改活动
 * @request POST:/91017/updateActivity
 */
export const updateActivity = (
  request: Activity91017CreateOrUpdateRequest,
): Promise<Activity91017CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/91017/updateActivity',
    method: 'post',
    data: request,
  });
};
