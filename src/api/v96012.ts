import {
  Activity96012ActivityDataLogReq,
  Activity96012ActivityDataLogResp,
  Activity96012ActivityDataRequest,
  Activity96012ActivityTimeDataRequest,
  Activity96012CreateOrUpdateRequest,
  Activity96012CreateOrUpdateResponse,
  Activity96012PrizeSendListReq,
  IPageActivity96012PrizeSendDetailListResp,
  IPageActivity96012ShareLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags vivo晒照有礼
 * @summary 创建活动
 * @request POST:/96012/createActivity
 */
export const createActivity = (
  request: Activity96012CreateOrUpdateRequest,
): Promise<Activity96012CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96012/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 获取活动数据
 * @request POST:/96012/data/activityData
 */
export const dataActivityData = (
  request: Activity96012ActivityDataLogReq,
): Promise<Activity96012ActivityDataLogResp> => {
  return httpRequest({
    url: '/96012/data/activityData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 获取活动数据导出
 * @request POST:/96012/data/activityData/export
 */
export const dataActivityDataExport = (request: Activity96012ActivityDataLogReq): Promise<void> => {
  return httpRequest({
    url: '/96012/data/activityData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 奖品发放明细
 * @request POST:/96012/data/prizeSendDetail
 */
export const dataPrizeSendDetail = (
  request: Activity96012PrizeSendListReq,
): Promise<IPageActivity96012PrizeSendDetailListResp> => {
  return httpRequest({
    url: '/96012/data/prizeSendDetail',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 奖品发放明细导出
 * @request POST:/96012/data/prizeSendDetail/export
 */
export const dataPrizeSendDetailExport = (request: Activity96012PrizeSendListReq): Promise<void> => {
  return httpRequest({
    url: '/96012/data/prizeSendDetail/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 晒单排名记录
 * @request POST:/96012/data/shareLog
 */
export const dataShareLog = (
  request: Activity96012ActivityDataRequest,
): Promise<IPageActivity96012ShareLogResponse> => {
  return httpRequest({
    url: '/96012/data/shareLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 删除
 * @request POST:/96012/data/shareLog/delete
 */
export const dataShareLogDelete = (request: Activity96012ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/96012/data/shareLog/delete',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 晒单排名记录导出
 * @request POST:/96012/data/shareLog/export
 */
export const dataShareLogExport = (request: Activity96012ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/96012/data/shareLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 开启排名
 * @request POST:/96012/data/shareLog/rankTime
 */
export const dataShareLogRankTime = (request: Activity96012ActivityTimeDataRequest): Promise<void> => {
  return httpRequest({
    url: '/96012/data/shareLog/rankTime',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 设置开奖时间
 * @request POST:/96012/data/shareLog/startTime
 */
export const dataShareLogStartTime = (request: Activity96012ActivityTimeDataRequest): Promise<void> => {
  return httpRequest({
    url: '/96012/data/shareLog/startTime',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 修改人气值
 * @request POST:/96012/data/shareLog/updateLike
 */
export const dataShareLogUpdateLike = (request: Activity96012ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/96012/data/shareLog/updateLike',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼数据
 * @summary 修改晒单排名
 * @request POST:/96012/data/shareLog/updateRank
 */
export const dataShareLogUpdateRank = (request: Activity96012ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/96012/data/shareLog/updateRank',
    method: 'post',
    data: request,
  });
};

/**
 * @tags vivo晒照有礼
 * @summary 修改活动
 * @request POST:/96012/updateActivity
 */
export const updateActivity = (
  request: Activity96012CreateOrUpdateRequest,
): Promise<Activity96012CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96012/updateActivity',
    method: 'post',
    data: request,
  });
};
