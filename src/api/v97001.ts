import {
  Activity97001CreateOrUpdateRequest,
  Activity97001CreateOrUpdateResponse,
  Activity97001DataPageRequest,
  Activity97001UserPrizeRecordPageRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity97001DataResponse,
  IPageActivity97001UserPrizeRecordPageResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 合生元1633星耀卡
 * @summary 创建活动
 * @request POST:/97001/createActivity
 */
export const createActivity = (
  request: Activity97001CreateOrUpdateRequest,
): Promise<Activity97001CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/97001/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 合生元1633星耀卡数据
 * @summary 报表导出
 * @request POST:/97001/data/data/export
 */
export const dataDataExport = (request: Activity97001DataPageRequest): Promise<void> => {
  return httpRequest({
    url: '/97001/data/data/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 合生元1633星耀卡数据
 * @summary 报表查询
 * @request POST:/97001/data/reportPage
 */
export const dataReportPage = (request: Activity97001DataPageRequest): Promise<IPageActivity97001DataResponse> => {
  return httpRequest({
    url: '/97001/data/reportPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 合生元1633星耀卡数据
 * @summary 中奖记录
 * @request POST:/97001/data/winningLog
 */
export const dataWinningLog = (
  request: Activity97001UserPrizeRecordPageRequest,
): Promise<IPageActivity97001UserPrizeRecordPageResponse> => {
  return httpRequest({
    url: '/97001/data/winningLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 合生元1633星耀卡数据
 * @summary 中奖记录导出
 * @request POST:/97001/data/winningLog/export
 */
export const dataWinningLogExport = (request: Activity97001UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/97001/data/winningLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 合生元1633星耀卡
 * @summary 查询活动信息
 * @request POST:/97001/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/97001/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 合生元1633星耀卡
 * @summary 修改活动
 * @request POST:/97001/updateActivity
 */
export const updateActivity = (
  request: Activity97001CreateOrUpdateRequest,
): Promise<Activity97001CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/97001/updateActivity',
    method: 'post',
    data: request,
  });
};
