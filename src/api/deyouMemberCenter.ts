import {
  ActivityAddDecorationDatasRequest,
  ActivityAddIPRequest,
  ActivityAddOrUpdateBackGroundAllRequest,
  ActivityAddOrUpdateIconAllRequest,
  ActivityAddOrUpdateIPRequest,
  ActivityDecorationDatasRequest,
  ActivityDecorationDatasResponse,
  ActivityDeleteIPRequest,
  ActivityDeYouSkuTemplateVo,
  ActivityGetBackGroundResponse,
  ActivityGetIPResponse,
  ActivitySaveSkuAllRequest,
  ActivityUpdateIPRequest,
  ActivityUrlResponse,
  GetCardGradeQueryResp,
  IPageActivityQueryIPResponse,
  QuerySkusUsingPostParams,
  QuerySkuUsingPostParams,
  UploadToOssUsingPostParams,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags deyou会员中心
 * @summary 添加修改装修数据
 * @request POST:/deyouMemberCenter/addDecorationData
 */
export const addDecorationData = (request: ActivityAddDecorationDatasRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/addDecorationData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags deyou会员中心
 * @summary 新增IP形象管理
 * @request POST:/deyouMemberCenter/addIP
 */
export const addIP = (addSkuRequest: ActivityAddIPRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/addIP',
    method: 'post',
    data: addSkuRequest,
  });
};

/**
 * @tags deyou会员中心
 * @summary 增删改背景样式列表
 * @request POST:/deyouMemberCenter/addOrupdateBackground
 */
export const addOrupdateBackground = (request: ActivityAddOrUpdateBackGroundAllRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/addOrupdateBackground',
    method: 'post',
    data: request,
  });
};

/**
 * @tags deyou会员中心
 * @summary 会员页/二级页 增加或修改或删除IP形象管理
 * @request POST:/deyouMemberCenter/addOrupdateIP
 */
export const addOrupdateIP = (request: ActivityAddOrUpdateIPRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/addOrupdateIP',
    method: 'post',
    data: request,
  });
};

/**
 * @tags deyou会员中心
 * @summary 增删改背景样式列表
 * @request POST:/deyouMemberCenter/addOrupdateIcon
 */
export const addOrupdateIcon = (request: ActivityAddOrUpdateIconAllRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/addOrupdateIcon',
    method: 'post',
    data: request,
  });
};

/**
 * @tags deyou会员中心
 * @summary 删IP形象管理
 * @request POST:/deyouMemberCenter/deleteIP
 */
export const deleteIP = (deleteSkuRequest: ActivityDeleteIPRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/deleteIP',
    method: 'post',
    data: deleteSkuRequest,
  });
};

/**
 * @tags deyou会员中心
 * @summary 下载购物配置skuId
 * @request POST:/deyouMemberCenter/downSku/export
 */
export const downSkuExport = (): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/downSku/export',
    method: 'post',
  });
};

/**
 * @tags deyou会员中心
 * @summary 查询背景样式列表
 * @request POST:/deyouMemberCenter/getBackground
 */
export const getBackground = (request: ActivityAddOrUpdateIPRequest): Promise<ActivityGetBackGroundResponse[]> => {
  return httpRequest({
    url: '/deyouMemberCenter/getBackground',
    method: 'post',
    data: request,
  });
};

/**
 * @tags deyou会员中心
 * @summary 获取装修数据
 * @request POST:/deyouMemberCenter/getDecorationData
 */
export const getDecorationData = (
  request: ActivityDecorationDatasRequest,
): Promise<ActivityDecorationDatasResponse[]> => {
  return httpRequest({
    url: '/deyouMemberCenter/getDecorationData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags deyou会员中心
 * @summary 查询已经添加的会员页/二级页 IP形象
 * @request POST:/deyouMemberCenter/getIP
 */
export const getIP = (request: ActivityAddOrUpdateIPRequest): Promise<ActivityGetIPResponse[]> => {
  return httpRequest({
    url: '/deyouMemberCenter/getIP',
    method: 'post',
    data: request,
  });
};

/**
 * @tags deyou会员中心
 * @summary 导入系列信息excel
 * @request POST:/deyouMemberCenter/importSeriesExcel
 */
export const importSeriesExcel = (file: any): Promise<ActivityDeYouSkuTemplateVo[]> => {
  return httpRequest({
    url: '/deyouMemberCenter/importSeriesExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags deyou会员中心
 * @summary 忠诚度店铺会员等级名称查询
 * @request POST:/deyouMemberCenter/memberLoyalty
 */
export const memberLoyalty = (): Promise<GetCardGradeQueryResp> => {
  return httpRequest({
    url: '/deyouMemberCenter/memberLoyalty',
    method: 'post',
  });
};

/**
 * @tags deyou会员中心
 * @summary 查询IP形象管理-二级页/会员页
 * @request POST:/deyouMemberCenter/queryIP
 */
export const queryIP = (query: QuerySkuUsingPostParams): Promise<IPageActivityQueryIPResponse> => {
  return httpRequest({
    url: '/deyouMemberCenter/queryIP',
    method: 'post',
    params: query,
  });
};

/**
 * @tags deyou会员中心
 * @summary 查询IP形象管理-B端形象管理页
 * @request POST:/deyouMemberCenter/queryIPs
 */
export const queryIPs = (query: QuerySkusUsingPostParams): Promise<IPageActivityQueryIPResponse> => {
  return httpRequest({
    url: '/deyouMemberCenter/queryIPs',
    method: 'post',
    params: query,
  });
};

/**
 * @tags deyou会员中心
 * @summary 保存购物配置skuId
 * @request POST:/deyouMemberCenter/saveSku
 */
export const saveSku = (addSkuRequest: ActivitySaveSkuAllRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/saveSku',
    method: 'post',
    data: addSkuRequest,
  });
};

/**
 * @tags deyou会员中心
 * @summary 下载模板
 * @request POST:/deyouMemberCenter/seriesTemplate/export
 */
export const seriesTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/seriesTemplate/export',
    method: 'post',
  });
};

/**
 * @tags deyou会员中心
 * @summary 修改IP形象管理
 * @request POST:/deyouMemberCenter/updateIP
 */
export const updateIP = (updateSkuRequest: ActivityUpdateIPRequest): Promise<void> => {
  return httpRequest({
    url: '/deyouMemberCenter/updateIP',
    method: 'post',
    data: updateSkuRequest,
  });
};

/**
 * @tags deyou会员中心
 * @summary 上传图片
 * @request POST:/deyouMemberCenter/uploadToOss
 */
export const uploadToOss = (query: UploadToOssUsingPostParams, file: any): Promise<ActivityUrlResponse> => {
  return httpRequest({
    url: '/deyouMemberCenter/uploadToOss',
    method: 'post',
    params: query,
    data: file,
  });
};
