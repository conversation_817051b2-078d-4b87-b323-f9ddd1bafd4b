import {
  Activity90203AddCouponRequest,
  Activity90203AddECardRequest,
  Activity90203AddLotteryCodeRequest,
  Activity90203AddPopCouponRequest,
  Activity90203CouponGrantRecordRequest,
  Activity90203CouponRequest,
  Activity90203CouponStatisticsResponse,
  Activity90203CreateOrUpdateRequest,
  Activity90203CreateOrUpdateResponse,
  Activity90203CrowdAddRequest,
  Activity90203CrowdDeleteRequest,
  Activity90203CrowdListRequest,
  Activity90203CrowdListResponse,
  Activity90203CrowdUpdateRequest,
  Activity90203CrowdUpdateStatusRequest,
  Activity90203DataIconResponse,
  Activity90203DataPrizeResponse,
  Activity90203DataRequest,
  Activity90203DeleteCouponRequest,
  Activity90203GetCardPoolRequest,
  Activity90203GetCardPoolResponse,
  Activity90203GetLotteryCodeListRequest,
  Activity90203GetLotteryCodeListUnboundPlanListRequest,
  Activity90203GetLotteryCodeRequest,
  Activity90203GetLotteryCodeResponse,
  Activity90203GetOneCouponRequest,
  Activity90203GetOneCouponResponse,
  Activity90203GetPlanListRequest,
  Activity90203GetPopOneCouponRequest,
  Activity90203GetPopOneCouponResponse,
  Activity90203GetSendRecordLotteryCodeRequest,
  Activity90203GetSendRecordRequest,
  Activity90203GetUnboundPlanListRequest,
  Activity90203OperationRequest,
  Activity90203PrizeRecordListRequest,
  Activity90203PrizeSendResponse,
  Activity90203StopPlanLotteryCodeRequest,
  Activity90203StopPlanRequest,
  IPageActivity90203CouponResponse,
  IPageActivity90203GetLotteryCodeListResponse,
  IPageActivity90203GetLotteryCodeListUnboundPlanListResponse,
  IPageActivity90203GetPlanListResponse,
  IPageActivity90203GetSendRecordLotteryCodeResponse,
  IPageActivity90203GetSendRecordResponse,
  IPageActivity90203GetUnboundPlanListResponse,
  IPageActivity90203OperationResponse,
  IPageActivity90203PrizeRecordListResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 90203列表
 * @summary 创建活动
 * @request POST:/90203/activity/createActivity
 */
export const activityCreateActivity = (
  request: Activity90203CreateOrUpdateRequest,
): Promise<Activity90203CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90203/activity/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203列表
 * @summary 奖品已发放数量
 * @request POST:/90203/activity/prizeCount
 */
export const activityPrizeCount = (activity90203PrizeSendResponse: Activity90203PrizeSendResponse): Promise<string> => {
  return httpRequest({
    url: '/90203/activity/prizeCount',
    method: 'post',
    data: activity90203PrizeSendResponse,
  });
};

/**
 * @tags 90203列表
 * @summary 修改活动
 * @request POST:/90203/activity/updateActivity
 */
export const activityUpdateActivity = (
  request: Activity90203CreateOrUpdateRequest,
): Promise<Activity90203CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90203/activity/updateActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 添加PoP优惠券
 * @request POST:/90203/asset/coupon/addPop
 */
export const assetCouponAddPop = (request: Activity90203AddPopCouponRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/coupon/addPop',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 添加自营优惠券
 * @request POST:/90203/asset/coupon/addSo
 */
export const assetCouponAddSo = (request: Activity90203AddCouponRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/coupon/addSo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 删除优惠券
 * @request POST:/90203/asset/coupon/delete
 */
export const assetCouponDelete = (request: Activity90203DeleteCouponRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/coupon/delete',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 生产发放优惠券ID
 * @request POST:/90203/asset/coupon/generateGrantCouponId
 */
export const assetCouponGenerateGrantCouponId = (): Promise<string> => {
  return httpRequest({
    url: '/90203/asset/coupon/generateGrantCouponId',
    method: 'post',
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 单个pop优惠券查询
 * @request POST:/90203/asset/coupon/getPopOneCoupon
 */
export const assetCouponGetPopOneCoupon = (
  query: Activity90203GetPopOneCouponRequest,
): Promise<Activity90203GetPopOneCouponResponse> => {
  return httpRequest({
    url: '/90203/asset/coupon/getPopOneCoupon',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 单个自营优惠券查询
 * @request POST:/90203/asset/coupon/getSoOneCoupon
 */
export const assetCouponGetSoOneCoupon = (
  query: Activity90203GetOneCouponRequest,
): Promise<Activity90203GetOneCouponResponse> => {
  return httpRequest({
    url: '/90203/asset/coupon/getSoOneCoupon',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 发放记录
 * @request POST:/90203/asset/coupon/grantRecord
 */
export const assetCouponGrantRecord = (
  query: Activity90203CouponGrantRecordRequest,
): Promise<Record<string, object>> => {
  return httpRequest({
    url: '/90203/asset/coupon/grantRecord',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 导出发放记录
 * @request POST:/90203/asset/coupon/grantRecord/export
 */
export const assetCouponGrantRecordExport = (query: Activity90203CouponGrantRecordRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/coupon/grantRecord/export',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 查询优惠券
 * @request POST:/90203/asset/coupon/list
 */
export const assetCouponList = (query: Activity90203CouponRequest): Promise<IPageActivity90203CouponResponse> => {
  return httpRequest({
    url: '/90203/asset/coupon/list',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 90203优惠券资产
 * @summary 优惠券数据统计
 * @request POST:/90203/asset/coupon/statistics
 */
export const assetCouponStatistics = (): Promise<Activity90203CouponStatisticsResponse> => {
  return httpRequest({
    url: '/90203/asset/coupon/statistics',
    method: 'post',
  });
};

/**
 * @tags 90203E卡资产
 * @summary 创建E卡
 * @request POST:/90203/asset/eCard/addECard
 */
export const assetECardAddECard = (activity90203AddECardRequest: Activity90203AddECardRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/eCard/addECard',
    method: 'post',
    data: activity90203AddECardRequest,
  });
};

/**
 * @tags 90203E卡资产
 * @summary 查询E卡详情
 * @request POST:/90203/asset/eCard/getCardPool
 */
export const assetECardGetCardPool = (
  activity90203GetCardPoolRequest: Activity90203GetCardPoolRequest,
): Promise<Activity90203GetCardPoolResponse> => {
  return httpRequest({
    url: '/90203/asset/eCard/getCardPool',
    method: 'post',
    data: activity90203GetCardPoolRequest,
  });
};

/**
 * @tags 90203E卡资产
 * @summary 计划列表
 * @request POST:/90203/asset/eCard/getPlanList
 */
export const assetECardGetPlanList = (
  activity90203GetPlanListRequest: Activity90203GetPlanListRequest,
): Promise<IPageActivity90203GetPlanListResponse> => {
  return httpRequest({
    url: '/90203/asset/eCard/getPlanList',
    method: 'post',
    data: activity90203GetPlanListRequest,
  });
};

/**
 * @tags 90203E卡资产
 * @summary 发放记录
 * @request POST:/90203/asset/eCard/getSendRecord
 */
export const assetECardGetSendRecord = (
  activity90203GetSendRecordRequest: Activity90203GetSendRecordRequest,
): Promise<IPageActivity90203GetSendRecordResponse> => {
  return httpRequest({
    url: '/90203/asset/eCard/getSendRecord',
    method: 'post',
    data: activity90203GetSendRecordRequest,
  });
};

/**
 * @tags 90203E卡资产
 * @summary 未绑定计划列表
 * @request POST:/90203/asset/eCard/getUnboundPlanList
 */
export const assetECardGetUnboundPlanList = (
  activity90203GetUnboundPlanListRequest: Activity90203GetUnboundPlanListRequest,
): Promise<IPageActivity90203GetUnboundPlanListResponse> => {
  return httpRequest({
    url: '/90203/asset/eCard/getUnboundPlanList',
    method: 'post',
    data: activity90203GetUnboundPlanListRequest,
  });
};

/**
 * @tags 90203E卡资产
 * @summary 发放记录导出
 * @request POST:/90203/asset/eCard/sendRecord/export
 */
export const assetECardSendRecordExport = (
  activity90203GetSendRecordRequest: Activity90203GetSendRecordRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/eCard/sendRecord/export',
    method: 'post',
    data: activity90203GetSendRecordRequest,
  });
};

/**
 * @tags 90203E卡资产
 * @summary 结束计划
 * @request POST:/90203/asset/eCard/stopPlan
 */
export const assetECardStopPlan = (activity90203StopPlanRequest: Activity90203StopPlanRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/eCard/stopPlan',
    method: 'post',
    data: activity90203StopPlanRequest,
  });
};

/**
 * @tags 90203领货码
 * @summary 创建领货码
 * @request POST:/90203/asset/lotteryCode/addLotteryCode
 */
export const assetLotteryCodeAddLotteryCode = (
  activity90203AddLotteryCodeRequest: Activity90203AddLotteryCodeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/lotteryCode/addLotteryCode',
    method: 'post',
    data: activity90203AddLotteryCodeRequest,
  });
};

/**
 * @tags 90203领货码
 * @summary 查询领货码详情
 * @request POST:/90203/asset/lotteryCode/getCardPool
 */
export const assetLotteryCodeGetCardPool = (
  activity90203GetLotteryCodeRequest: Activity90203GetLotteryCodeRequest,
): Promise<Activity90203GetLotteryCodeResponse> => {
  return httpRequest({
    url: '/90203/asset/lotteryCode/getCardPool',
    method: 'post',
    data: activity90203GetLotteryCodeRequest,
  });
};

/**
 * @tags 90203领货码
 * @summary 计划列表
 * @request POST:/90203/asset/lotteryCode/getPlanList
 */
export const assetLotteryCodeGetPlanList = (
  activity90203GetLotteryCodeListRequest: Activity90203GetLotteryCodeListRequest,
): Promise<IPageActivity90203GetLotteryCodeListResponse> => {
  return httpRequest({
    url: '/90203/asset/lotteryCode/getPlanList',
    method: 'post',
    data: activity90203GetLotteryCodeListRequest,
  });
};

/**
 * @tags 90203领货码
 * @summary 发放记录
 * @request POST:/90203/asset/lotteryCode/getSendRecord
 */
export const assetLotteryCodeGetSendRecord = (
  activity90203GetSendRecordRequest: Activity90203GetSendRecordLotteryCodeRequest,
): Promise<IPageActivity90203GetSendRecordLotteryCodeResponse> => {
  return httpRequest({
    url: '/90203/asset/lotteryCode/getSendRecord',
    method: 'post',
    data: activity90203GetSendRecordRequest,
  });
};

/**
 * @tags 90203领货码
 * @summary 未绑定计划列表
 * @request POST:/90203/asset/lotteryCode/getUnboundPlanList
 */
export const assetLotteryCodeGetUnboundPlanList = (
  activity90203GetUnboundPlanListRequest: Activity90203GetLotteryCodeListUnboundPlanListRequest,
): Promise<IPageActivity90203GetLotteryCodeListUnboundPlanListResponse> => {
  return httpRequest({
    url: '/90203/asset/lotteryCode/getUnboundPlanList',
    method: 'post',
    data: activity90203GetUnboundPlanListRequest,
  });
};

/**
 * @tags 90203领货码
 * @summary 发放记录导出
 * @request POST:/90203/asset/lotteryCode/sendRecord/export
 */
export const assetLotteryCodeSendRecordExport = (
  activity90203GetSendRecordRequest: Activity90203GetSendRecordLotteryCodeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/lotteryCode/sendRecord/export',
    method: 'post',
    data: activity90203GetSendRecordRequest,
  });
};

/**
 * @tags 90203领货码
 * @summary 结束计划
 * @request POST:/90203/asset/lotteryCode/stopPlan
 */
export const assetLotteryCodeStopPlan = (
  activity90203StopPlanLotteryCodeRequest: Activity90203StopPlanLotteryCodeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90203/asset/lotteryCode/stopPlan',
    method: 'post',
    data: activity90203StopPlanLotteryCodeRequest,
  });
};

/**
 * @tags 90203列表
 * @summary 新增人群
 * @request POST:/90203/crowd/add
 */
export const crowdAdd = (request: Activity90203CrowdAddRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/crowd/add',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203列表
 * @summary 删除人群
 * @request POST:/90203/crowd/delete
 */
export const crowdDelete = (request: Activity90203CrowdDeleteRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/crowd/delete',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203列表
 * @summary 查询人群
 * @request POST:/90203/crowd/list
 */
export const crowdList = (request: Activity90203CrowdListRequest): Promise<Activity90203CrowdListResponse[]> => {
  return httpRequest({
    url: '/90203/crowd/list',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203列表
 * @summary 修改人群
 * @request POST:/90203/crowd/update
 */
export const crowdUpdate = (request: Activity90203CrowdUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/crowd/update',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203列表
 * @summary 修改状态
 * @request POST:/90203/crowd/updateStatus
 */
export const crowdUpdateStatus = (request: Activity90203CrowdUpdateStatusRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/crowd/updateStatus',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 90203数据
 * @summary 活动Icon数据
 * @request POST:/90203/data/dataIcon
 */
export const dataDataIcon = (req: Activity90203DataRequest): Promise<Activity90203DataIconResponse[]> => {
  return httpRequest({
    url: '/90203/data/dataIcon',
    method: 'post',
    data: req,
  });
};

/**
 * @tags 90203数据
 * @summary 活动Icon数据导出
 * @request POST:/90203/data/dataIcon/export
 */
export const dataDataIconExport = (req: Activity90203DataRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/data/dataIcon/export',
    method: 'post',
    data: req,
  });
};

/**
 * @tags 90203数据
 * @summary 活动奖品数据
 * @request POST:/90203/data/dataPrize
 */
export const dataDataPrize = (req: Activity90203DataRequest): Promise<Activity90203DataPrizeResponse[]> => {
  return httpRequest({
    url: '/90203/data/dataPrize',
    method: 'post',
    data: req,
  });
};

/**
 * @tags 90203数据
 * @summary 活动奖品数据导出
 * @request POST:/90203/data/dataPrize/export
 */
export const dataDataPrizeExport = (req: Activity90203DataRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/data/dataPrize/export',
    method: 'post',
    data: req,
  });
};

/**
 * @tags 90203数据
 * @summary 领取记录
 * @request POST:/90203/data/prizeRecord
 */
export const dataPrizeRecord = (
  req: Activity90203PrizeRecordListRequest,
): Promise<IPageActivity90203PrizeRecordListResponse> => {
  return httpRequest({
    url: '/90203/data/prizeRecord',
    method: 'post',
    data: req,
  });
};

/**
 * @tags 90203数据
 * @summary 领取记录导出
 * @request POST:/90203/data/prizeRecord/export
 */
export const dataPrizeRecordExport = (req: Activity90203PrizeRecordListRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/data/prizeRecord/export',
    method: 'post',
    data: req,
  });
};

/**
 * @tags 90203日志
 * @summary 操作日志
 * @request POST:/90203/log/operation
 */
export const logOperation = (
  activity90203OperationRequest: Activity90203OperationRequest,
): Promise<IPageActivity90203OperationResponse> => {
  return httpRequest({
    url: '/90203/log/operation',
    method: 'post',
    data: activity90203OperationRequest,
  });
};

/**
 * @tags 90203日志
 * @summary 操作日志导出
 * @request POST:/90203/log/operation/export
 */
export const logOperationExport = (activity90203OperationRequest: Activity90203OperationRequest): Promise<void> => {
  return httpRequest({
    url: '/90203/log/operation/export',
    method: 'post',
    data: activity90203OperationRequest,
  });
};
