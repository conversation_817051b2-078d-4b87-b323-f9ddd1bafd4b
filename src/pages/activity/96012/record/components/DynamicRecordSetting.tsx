import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Icon, Input, Message, Table } from '@alifd/next';
import LzDialog from '@/components/LzDialog';
import { dataShareLog, orderUse, saveAddress } from '@/api/v96012';
import Utils, { getParams } from '@/utils';
import format from '@/utils/format';
import { PRIZE_TYPE } from '../../2001/util';
import constant from '@/utils/constant';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;

export default (props) => {
  const field = Field.useField();
  const addressField = Field.useField();
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];
  // 弹窗状态
  const [orderDetailVisible, setOrderDetailVisible] = useState(false);
  const [addressVisible, setAddressVisible] = useState(false);
  const [confirmWriteOffVisible, setConfirmWriteOffVisible] = useState(false);
  const [choosePrizeVisible, setChoosePrizeVisible] = useState(false);

  // 当前操作的数据
  const [currentData, setCurrentData] = useState<any>(null);
  const [orderDetailData, setOrderDetailData] = useState<any[]>([]);
  const [choosePrizeList, setChoosePrizeList] = useState<any[]>([]);
  const [selectedPrize, setSelectedPrize] = useState<any>(null);

  // 地址数据
  const [addressData, setAddressData] = useState({
    realName: '',
    mobile: '',
    province: '',
    city: '',
    county: '',
    address: '',
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataShareLog(query)
      .then((res: any): void => {
        setTableData(res as any[]);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue });
  }, []);

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="动态发布时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <Form.Item name="rank" label="用户排名">
          <Input placeholder="请输入用户排名" type={'number'} />
        </Form.Item>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue });
            }}
          >
            重置
          </Form.Reset>
          {/* <Button onClick={exportData} style={{ marginRight: '8px' }}> */}
          {/*  导出 */}
          {/* </Button> */}
          {/* <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover"> */}
          {/*  每次最多仅支持导出1000条数据 */}
          {/* </Balloon> */}
        </FormItem>
      </Form>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="发布时间" dataIndex="createTime" />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column title="排名" dataIndex="rank" />
        <Table.Column title="人气值" dataIndex="fans" />
        <Table.Column title="父分类" dataIndex="fatherSection" />
        <Table.Column title="子分类" dataIndex="childSection" />
        <Table.Column title="标题" dataIndex="title" />
        <Table.Column title="内容" dataIndex="content" />
        <Table.Column
          title="图片信息"
          dataIndex="imgList"
          cell={(value, index, data) => (
            <div>
              {value.map((item, index) => (
                <img src={item} style={{ width: '60px', marginRight: '' }} />
              ))}
            </div>
          )}
        />
        <Table.Column
          title="操作"
          cell={(value, index, data) => (
            <div>
              <Button
                style={{ marginRight: '8px' }}
                text
                type="primary"
                onClick={() => {
                  // 模拟获取订单商品数据
                  setOrderDetailData(data.orderSkuInfo);
                  setOrderDetailVisible(true);
                }}
              >
                查看下单商品
              </Button>
              <Button
                style={{ marginRight: '8px' }}
                text
                // disabled={!(!data.addressInfo && data.verifyStatus === '已核销')}
                disabled={!(data.verifyStatus === '已核销' && data.prizeType === '3')}
                type="primary"
                onClick={() => {
                  setCurrentData(data);
                  setAddressVisible(true);
                }}
              >
                登记地址
              </Button>
              <Button
                text
                disabled={data.verifyStatus === '已核销'}
                type="primary"
                onClick={() => {
                  setCurrentData(data);
                  setConfirmWriteOffVisible(true);
                }}
              >
                核销
              </Button>
            </div>
          )}
        />
      </Table>

      {/* 查看下单商品弹窗 */}
      <LzDialog
        title="下单商品详情"
        visible={orderDetailVisible}
        footer={false}
        onClose={() => setOrderDetailVisible(false)}
        style={{ width: '600px' }}
      >
        <Table dataSource={orderDetailData}>
          <Table.Column title="商品ID" dataIndex="skuId" />
          <Table.Column title="商品名" dataIndex="skuName" />
          <Table.Column title="数量" dataIndex="itemTotal" />
        </Table>
        <div style={{ textAlign: 'right', marginTop: '20px' }}>
          <Button onClick={() => setOrderDetailVisible(false)}>关闭</Button>
        </div>
      </LzDialog>

      {/* 登记地址弹窗 */}
      <LzDialog
        title="登记地址"
        visible={addressVisible}
        footer={false}
        onClose={() => setAddressVisible(false)}
        style={{ width: '500px' }}
      >
        <Form field={addressField} colon labelAlign={'left'}>
          <Form.Item label="收货人" required>
            <Input
              placeholder="请输入收货人姓名"
              value={addressData.realName}
              maxLength={50}
              showLimitHint
              onChange={(value) => setAddressData({ ...addressData, realName: String(value) })}
            />
          </Form.Item>
          <Form.Item label="手机号" required>
            <Input
              placeholder="请输入手机号"
              value={addressData.mobile}
              maxLength={11}
              showLimitHint
              onChange={(value) => setAddressData({ ...addressData, mobile: String(value) })}
            />
          </Form.Item>
          <Form.Item label="省" required>
            <Input
              placeholder="请输入省份"
              maxLength={50}
              showLimitHint
              value={addressData.province}
              onChange={(value) => setAddressData({ ...addressData, province: String(value) })}
            />
          </Form.Item>
          <Form.Item label="市" required>
            <Input
              placeholder="请输入城市"
              maxLength={50}
              showLimitHint
              value={addressData.city}
              onChange={(value) => setAddressData({ ...addressData, city: String(value) })}
            />
          </Form.Item>
          <Form.Item label="区" required>
            <Input
              placeholder="请输入区县"
              maxLength={50}
              showLimitHint
              value={addressData.county}
              onChange={(value) => setAddressData({ ...addressData, county: String(value) })}
            />
          </Form.Item>
          <Form.Item label="详细地址" required>
            <Input.TextArea
              placeholder="请输入详细地址"
              value={addressData.address}
              onChange={(value) => setAddressData({ ...addressData, address: String(value) })}
              rows={3}
              showLimitHint
              maxLength={100}
            />
          </Form.Item>
        </Form>
        <div style={{ textAlign: 'right', marginTop: '20px' }}>
          <Button
            type="primary"
            style={{ marginRight: '10px' }}
            onClick={() => {
              if (!addressData.realName) {
                Message.error('请输入收货人姓名');
                return;
              }
              if (!addressData.mobile) {
                Message.error('请输入手机号');
                return;
              }
              if (!checkPhone(addressData.mobile)) {
                return;
              }
              if (!addressData.province) {
                Message.error('请输入省');
                return;
              }
              if (!addressData.city) {
                Message.error('请输入市');
                return;
              }
              if (!addressData.county) {
                Message.error('请输入区县');
                return;
              }
              if (!addressData.address) {
                Message.error('请输入详细地址');
                return;
              }
              // 这里调用保存地址的API
              saveAddress({
                ...addressData,
                pin: currentData.pin,
                activityId: getParams('id'),
                prizeRecordId: currentData.prizeRecordId,
              })
                .then(() => {
                  Message.success('地址保存成功');
                  // 更新currentData中的地址信息
                  setCurrentData({
                    ...currentData,
                    addressInfo: { ...addressData },
                  });
                  // 刷新页面数据
                  const formValue: any = field.getValues();
                  if (formValue.pin) {
                    formValue.pin = currentData.pin;
                  }
                  if (formValue.orderId) {
                    formValue.orderId = currentData.orderId;
                  }
                  loadData({ ...formValue });
                  setAddressVisible(false);
                })
                .catch((e) => {
                  Message.error(e.message);
                });
              console.log('保存地址:', addressData);
              setAddressVisible(false);
            }}
          >
            保存
          </Button>
          <Button onClick={() => setAddressVisible(false)}>取消</Button>
        </div>
      </LzDialog>

      {/* 确认核销弹窗 */}
      <LzDialog
        title="确认核销"
        visible={confirmWriteOffVisible}
        footer={false}
        onClose={() => setConfirmWriteOffVisible(false)}
        style={{ width: '400px' }}
      >
        <div style={{ padding: '20px 0', textAlign: 'center' }}>
          <div style={{ marginBottom: '20px', fontSize: '16px' }}>确认要核销该订单吗？</div>
          <div style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              style={{ marginRight: '10px' }}
              onClick={() => {
                setConfirmWriteOffVisible(false);
                // 处理奖品信息，确保库存字段正确设置
                const prizeList = (currentData.orderPrizeInfo || []).map((prize, index) => {
                  // 如果接口没有返回库存字段，默认设置为0
                  const stock = prize.stock !== undefined ? prize.stock : 0;

                  return {
                    ...prize,
                    id: `${prize.prizeId || prize.id || index}`, // 添加唯一ID
                    prizeName: `${prize.prizeName || ''} (${prize.seriesName || ''})`, // 组合显示名称
                    stock, // 奖品剩余数量
                    ckeckStatus: false, // 选中状态
                  };
                });
                setChoosePrizeList(prizeList);
                setChoosePrizeVisible(true);
              }}
            >
              确认
            </Button>
            <Button onClick={() => setConfirmWriteOffVisible(false)}>取消</Button>
          </div>
        </div>
      </LzDialog>

      {/* 选择核销奖品弹窗 */}
      <LzDialog
        title="选择核销奖品"
        visible={choosePrizeVisible}
        footer={false}
        onClose={() => {
          setChoosePrizeVisible(false);
          setSelectedPrize(null);
          // 重置选中状态，但避免不必要的状态更新
          setChoosePrizeList((prev) => {
            const resetList = prev.map((item) => ({ ...item, ckeckStatus: false }));
            // 只有当确实需要更新时才返回新数组
            return resetList.some((item) => item.ckeckStatus) ? resetList : prev;
          });
        }}
        style={{ width: '600px' }}
      >
        <div style={{ padding: '20px' }}>
          <div
            className="content"
            style={{
              maxHeight: '300px',
              overflowY: 'auto',
              marginBottom: '20px',
            }}
          >
            {choosePrizeList.map((item, index) => {
              // 判断奖品是否可选（库存大于0）
              const isSelectable = item.stock > 0;
              // 为每个奖品生成稳定的key
              const itemKey = item.id || `prize-${index}`;

              return (
                <div
                  key={itemKey}
                  className="prizeItem"
                  style={{
                    border: item.ckeckStatus ? '2px solid #1890ff' : '1px solid #e8e8e8',
                    borderRadius: '4px',
                    padding: '12px',
                    marginBottom: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    cursor: isSelectable ? 'pointer' : 'not-allowed',
                    backgroundColor: '#fafafa',
                  }}
                  onClick={() => {
                    // 如果奖品库存为0，不可选择
                    if (!isSelectable) {
                      Message.warning('该奖品库存不足，无法选择');
                      return;
                    }
                    // 如果当前奖品已选中，则取消选中
                    if (item.ckeckStatus) {
                      setChoosePrizeList((prev) =>
                        prev.map((prize) => (prize.id === item.id ? { ...prize, ckeckStatus: false } : prize)),
                      );
                      setSelectedPrize(null);
                      return;
                    }
                    // 先将所有奖品设为未选中，然后选中当前奖品
                    setChoosePrizeList((prev) =>
                      prev.map((prize) => ({
                        ...prize,
                        ckeckStatus: prize.id === item.id,
                      })),
                    );
                    setSelectedPrize(item);
                  }}
                >
                  <div
                    className={item.ckeckStatus ? 'checked' : 'unchecked'}
                    style={{
                      width: '16px',
                      height: '16px',
                      border: item.ckeckStatus ? '1px solid #1890ff' : '1px solid #d9d9d9',
                      borderRadius: '50%',
                      backgroundColor: item.ckeckStatus ? '#1890ff' : '#fff',
                      marginRight: '12px',
                      position: 'relative',
                    }}
                  >
                    {item.ckeckStatus && (
                      <div
                        style={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          width: '6px',
                          height: '6px',
                          backgroundColor: '#fff',
                          borderRadius: '50%',
                          transform: 'translate(-50%, -50%)',
                        }}
                      />
                    )}
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{item.prizeName || '未知奖品'}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        剩余库存: {item.stock !== undefined ? item.stock : 0}件
                      </div>
                      {!isSelectable && <div style={{ fontSize: '12px', color: '#ff4d4f' }}>库存不足</div>}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <div
            className="btnList"
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            <Button
              type="primary"
              style={{ marginRight: '10px' }}
              onClick={() => {
                if (!selectedPrize) {
                  Message.error('请选择奖品');
                  return;
                }
                // 这里调用核销API
                orderUse({
                  activityId: getParams('id'),
                  prizeId: selectedPrize.prizeId,
                  nickName: currentData.nickName,
                  pin: currentData.pin,
                  orderId: currentData.orderId,
                })
                  .then(() => {
                    console.log('确认核销奖品:', selectedPrize);
                    Message.success('核销成功');
                    // 刷新页面数据
                    const formValue: any = field.getValues();
                    if (formValue.pin) {
                      formValue.pin = currentData.pin;
                    }
                    if (formValue.orderId) {
                      formValue.orderId = currentData.orderId;
                    }
                    loadData({ ...formValue });
                  })
                  .catch((e) => {
                    Message.error(e.message);
                  });
                setChoosePrizeVisible(false);
                setSelectedPrize(null);
                setChoosePrizeList((prev) => prev.map((item) => ({ ...item, ckeckStatus: false })));
              }}
            >
              确认核销
            </Button>
            <Button
              onClick={() => {
                setChoosePrizeVisible(false);
                setSelectedPrize(null);
                setChoosePrizeList((prev) => prev.map((item) => ({ ...item, ckeckStatus: false })));
              }}
            >
              取消
            </Button>
          </div>
        </div>
      </LzDialog>
    </div>
  );
};
