import React, { useEffect, useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LotteryRecord from './components/LotteryRecord';
import DynamicRecordSetting from './components/DynamicRecordSetting';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="vivo晒单有礼-记录">
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="奖品发放记录" key="1">
            <LotteryRecord />
          </Tab.Item>
          <Tab.Item title="动态操作" key="2">
            <DynamicRecordSetting />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
