import React, { useReducer, useImperativeHandle, useState, useEffect } from 'react';
import { Form, Field, Button, Table, Message, Dialog, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrizeForDZ from '@/components/ChoosePrizeForDZ';
import { PageData, FormLayout } from '../../../util';
import { activityEditDisabled } from '@/utils';
import { PRIZE_TYPE } from '@/pages/activity/96012/2001/util';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  // 添加奖品弹窗
  const [visible, setVisible] = useState<boolean>(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState<any>(null);
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  // 当前编辑的奖品在奖品列表中的索引
  const [prizeIndex, setPrizeIndex] = useState<number>(0);

  // 设置获奖排名
  const setRankStr = (rankPrizesList) => {
    // 使用展开运算符保持数据不可变性
    const newRankPrizesList = [...rankPrizesList];
    let total1 = 0;
    let total2 = 0;
    for (let i = 0; i < newRankPrizesList.length; i++) {
      const send = newRankPrizesList[i].sendTotalCount;
      total2 += newRankPrizesList[i].sendTotalCount;
      total1 = total2 - send + 1;
      if (newRankPrizesList[i].sendTotalCount) {
        if (total1 === total2) {
          newRankPrizesList[i].rank = `${total1}`;
        } else {
          newRankPrizesList[i].rank = `${total1}-${total2}`;
        }
      } else {
        // 如果sendTotalCount为0，rank字段设为空
        newRankPrizesList[i].rank = '';
      }
    }
    console.log('change---', newRankPrizesList);
    return newRankPrizesList;
  };

  // 新建/编辑奖品回调
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index奖品信息
    if (tableName === 'rankPrizesList') {
      const newRankPrizesList = [...formData.rankPrizesList];
      const originalStatus = newRankPrizesList[prizeIndex]?.status ?? 1; // 默认上架
      const originalPhysicalType = newRankPrizesList[prizeIndex]?.physicalType ?? 0;
      newRankPrizesList[prizeIndex] = {
        ...data,
        sortId: prizeIndex, // 添加sortId字段，值为数组下标
        status: originalStatus,
        physicalType: originalPhysicalType,
        rank: '', // 初始化rank字段
      };
      // 先设置数据，再更新rank字段
      setData({ rankPrizesList: newRankPrizesList });
      // 使用setRankStr返回的新数组更新数据
      const rankedList = setRankStr(newRankPrizesList);
      setData({ rankPrizesList: rankedList });
    }
    setVisible(false);
  };

  const onCancel = (): void => {
    setVisible(false);
  };

  // 同步/更新数据
  const setData = (data): void => {
    // 创建新的formData对象以确保正确触发重新渲染
    const newFormData = { ...formData, ...data };
    setFormData(newFormData);
    onChange(newFormData, 'task');
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="排行榜设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="排行榜奖品设置" required requiredMessage="请选择奖品">
            <Button
              type="primary"
              style={{ marginBottom: 10 }}
              onClick={() => {
                // 添加一个新的空奖品
                let newRankPrizesList = [...formData.rankPrizesList];
                if (!newRankPrizesList) {
                  newRankPrizesList = [];
                }
                const newPrizeIndex = newRankPrizesList.length;
                newRankPrizesList.push({
                  prizeName: '',
                  prizeType: 1,
                  numPerSending: 0,
                  unitCount: 0,
                  sendTotalCount: 0,
                  unitPrice: 0,
                  prizeImg: '',
                  status: 1, // 默认上架
                  physicalType: 0,
                  sortId: newPrizeIndex,
                  rank: '', // 初始化rank字段
                });
                setData({ rankPrizesList: newRankPrizesList });
                // 使用setRankStr返回的新数组更新数据
                const rankedList = setRankStr(newRankPrizesList);
                setData({ rankPrizesList: rankedList });

                // 添加奖品后自动弹出奖品选择弹窗
                setEditValue(null); // 新添加的奖品使用 null，让组件显示类型选择
                setPrizeIndex(newPrizeIndex); // 设置当前奖品索引
                setTableName('rankPrizesList'); // 设置表格名
                setVisible(true); // 弹出奖品选择弹窗
              }}
            >
              添加奖品
            </Button>
            <Table dataSource={formData.rankPrizesList || []}>
              <Table.Column
                title="获奖排名"
                dataIndex="rank"
                cell={(_, index, row) => <div>排名第{formData.rankPrizesList[index].rank}名</div>}
              />
              <Table.Column title="奖品名称" dataIndex="prizeName" />
              <Table.Column
                title="奖品类型"
                cell={(_, prizeRowIndex, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="实物类型"
                cell={(_, prizeRowIndex, row) => {
                  const physicalType = row.physicalType ?? 0;
                  return <div>{physicalType === 1 ? '联系客服' : '--'}</div>;
                }}
              />
              <Table.Column
                title="单位数量"
                cell={(_, prizeRowIndex, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, prizeRowIndex, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, prizeRowIndex, row) => (
                  <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                )}
              />
              <Table.Column
                title="奖品图"
                cell={(_, prizeRowIndex, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="状态"
                cell={(_, prizeRowIndex, row) => {
                  const status = row.status ?? 1; // 默认上架
                  return (
                    <div style={{ color: status === 1 ? '#00d76a' : '#FD7622' }}>{status === 1 ? '上架' : '下架'}</div>
                  );
                }}
              />
              <Table.Column
                title="操作"
                width={200}
                cell={(prizeTableIndex, prizeRowIndex, row) => (
                  <FormItem disabled={false}>
                    <Button
                      text
                      type="primary"
                      onClick={() => {
                        const currentPrize = formData.rankPrizesList?.[prizeRowIndex];
                        if (currentPrize) {
                          const prizeData = currentPrize.prizeName ? currentPrize : {};
                          setEditValue(prizeData);
                          setPrizeIndex(prizeRowIndex); // 奖品索引
                          setTableName('rankPrizesList');
                          setVisible(true);
                        }
                      }}
                    >
                      编辑
                    </Button>

                    {/* 上架/下架按钮 */}
                    {row.status === 1 || row.status === undefined ? (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          // 检查当前系列中上架的奖品数量
                          const currentStep = formData;
                          if (currentStep) {
                            // 计算当前上架的奖品数量（status为1或undefined表示上架）
                            const activePrizeCount = currentStep.rankPrizesList.filter(
                              (prize, idx) =>
                                idx !== prizeRowIndex && (prize.status === 1 || prize.status === undefined),
                            ).length;

                            // 如果当前操作的奖品是上架状态，且下架后将没有上架的奖品，则提示用户
                            const isCurrentPrizeActive = row.status === 1 || row.status === undefined;
                            if (isCurrentPrizeActive && activePrizeCount === 0) {
                              Message.warning('至少保留一个上架的奖品');
                              return;
                            }
                          }

                          Dialog.confirm({
                            v2: true,
                            title: '提示',
                            centered: true,
                            content: '确认下架该奖品？',
                            onOk: () => {
                              const newRankPrizesList = [...formData.rankPrizesList];
                              if (newRankPrizesList[prizeRowIndex]) {
                                newRankPrizesList[prizeRowIndex].status = 0;
                                setData({ rankPrizesList: newRankPrizesList });
                                // 使用setRankStr返回的新数组更新数据
                                const rankedList = setRankStr(newRankPrizesList);
                                setData({ rankPrizesList: rankedList });
                              }
                            },
                            onCancel: () => console.log('cancel'),
                          } as any);
                        }}
                        title="下架"
                      >
                        下架
                      </Button>
                    ) : (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          const newRankPrizesList = [...formData.rankPrizesList];
                          if (newRankPrizesList[prizeRowIndex]) {
                            newRankPrizesList[prizeRowIndex].status = 1;
                            setData({ rankPrizesList: newRankPrizesList });
                            // 使用setRankStr返回的新数组更新数据
                            const rankedList = setRankStr(newRankPrizesList);
                            setData({ rankPrizesList: rankedList });
                          }
                        }}
                        title="上架"
                      >
                        上架
                      </Button>
                    )}

                    {/* 删除按钮：编辑进行中的活动不显示，但是如果是本次新添加的奖品，展示删除按钮 */}
                    {
                      // 在新建活动中，只要奖品数量大于1就显示删除按钮
                      // 在编辑进行中的活动时，判断是否为新添加的奖品才显示删除按钮
                      ((!activityEditDisabled() && (formData.rankPrizesList?.length || 0) > 1) ||
                        (activityEditDisabled() &&
                          (!row.prizeName || !defaultValue?.rankPrizesList?.[prizeTableIndex]) &&
                          (formData.rankPrizesList?.length || 0) > 1)) && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            // 检查删除后是否至少保留一个上架的奖品
                            // 计算当前上架的奖品数量（status为1或undefined表示上架）
                            const activePrizeCount = formData.rankPrizesList.filter(
                              (prize, idx) =>
                                idx !== prizeTableIndex && (prize.status === 1 || prize.status === undefined),
                            ).length;

                            // 如果当前操作的奖品是上架状态，且删除后将没有上架的奖品，则提示用户
                            const isCurrentPrizeActive = row.status === 1 || row.status === undefined;
                            if (isCurrentPrizeActive && activePrizeCount === 0) {
                              Message.warning('至少保留一个上架的奖品');
                              return;
                            }

                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该奖品？',
                              onOk: () => {
                                const newRankPrizesList = [...formData.rankPrizesList];
                                newRankPrizesList.splice(prizeTableIndex, 1);
                                // 重新设置sortId，确保对应数组下标
                                newRankPrizesList.forEach((prize, idx) => {
                                  prize.sortId = idx;
                                  prize.rank = '';
                                });
                                setData({ rankPrizesList: newRankPrizesList });
                                // 使用setRankStr返回的新数组更新数据
                                const rankedList = setRankStr(newRankPrizesList);
                                setData({ rankPrizesList: rankedList });
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                          title="删除"
                        >
                          删除
                        </Button>
                      )
                    }

                    {/** 设置为联系客服类型的实物奖品 */}
                    {row.prizeType === 3 && row.physicalType === 0 && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            const newRankPrizesList = [...formData.rankPrizesList];
                            if (newRankPrizesList[prizeRowIndex]) {
                              newRankPrizesList[prizeRowIndex].physicalType = 1;
                              setData({ rankPrizesList: newRankPrizesList });
                              // 使用setRankStr返回的新数组更新数据
                              const rankedList = setRankStr(newRankPrizesList);
                              setData({ rankPrizesList: rankedList });
                            }
                          }}
                          title="设置为联系客服的奖品"
                        >
                          设为联系客服
                        </Button>
                      </>
                    )}
                    {row.prizeType === 3 && row.physicalType === 1 && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            const newRankPrizesList = [...formData.rankPrizesList];
                            if (newRankPrizesList[prizeRowIndex]) {
                              newRankPrizesList[prizeRowIndex].physicalType = 0;
                              setData({ rankPrizesList: newRankPrizesList });
                              // 使用setRankStr返回的新数组更新数据
                              const rankedList = setRankStr(newRankPrizesList);
                              setData({ rankPrizesList: rankedList });
                            }
                          }}
                          title="取消设置为联系客服的奖品"
                        >
                          取消联系客服
                        </Button>
                      </>
                    )}
                  </FormItem>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="排行榜规则设置" required requiredMessage="请填写排行榜规则">
            <Input.TextArea
              value={formData.rankRule}
              name="rankRule"
              onChange={(rankRule) => setData({ rankRule })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入排行榜规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        className="lz-dialog-small"
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
      >
        <ChoosePrizeForDZ
          editValue={editValue}
          defaultEditValue={null}
          formData={formData}
          prizeNameLength={20}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
          typeList={[2, 1, 8, 7, 3]}
          defaultTarget={2}
        />
      </LzDialog>
    </div>
  );
};
