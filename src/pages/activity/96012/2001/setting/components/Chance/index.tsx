import React, { useReducer, useImperativeHandle, useState, useEffect } from 'react';
import { Form, Field, Radio, Button, Icon, Table } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';

import LzTask from '../LzTaskEdit';
import LzMsg from '@/components/LzMsg';
import { PageData, FormLayout } from '../../../util';
import { renderTaskGift, renderTaskContent, renderTaskLength, TASK_TYPE, renderTaskDate } from './taskRender';
import { activityEditDisabled } from '@/utils';
import format from '@/utils/format';

const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 监听活动时间变化并同步更新taskList中的任务时间
  useEffect(() => {
    if (value && value.rangeDate) {
      // 检查活动时间是否发生变化
      const isRangeDateChanged =
        value.rangeDate[0].valueOf() !== (formData.rangeDate ? formData.rangeDate[0].valueOf() : null) ||
        value.rangeDate[1].valueOf() !== (formData.rangeDate ? formData.rangeDate[1].valueOf() : null);

      if (isRangeDateChanged) {
        // 更新formData中的rangeDate
        setFormData({ rangeDate: value.rangeDate });

        // 如果taskList存在且不为空，更新任务时间
        if (value.taskList && Array.isArray(value.taskList) && value.taskList.length > 0) {
          const updatedTaskList = value.taskList.map((task) => {
            // 只有当任务时间限制方式为1（活动期间有效）时才更新时间
            if (task.taskTimeLimitWay === 1) {
              return {
                ...task,
                taskStartTime: format.formatDateTimeDayjs(value.rangeDate[0]),
                taskEndTime: format.formatDateTimeDayjs(value.rangeDate[1]),
              };
            }
            return task;
          });

          // 触发onChange更新父组件数据
          const updatedData: Required<PageData> = {
            ...defaultValue,
            ...value,
            taskList: updatedTaskList,
          };
          onChange(updatedData, 'task');
        }
      }
    }
  }, [value]);

  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  // 添加任务弹窗
  const [visible, setVisible] = useState<boolean>(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState<any>(null);
  // 当前编辑行数据
  const [initValue, setInitValue] = useState<any>(null);
  // 当前编辑index
  const [target, setTarget] = useState<number | null>(null);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data }, 'task');
  };
  /**
   * 添加任务组件成功回调
   * 向任务列表中添加数据 & 同步数据
   * @param taskInfo 任务信息
   */
  const handleSubmit = (taskInfo: any): void => {
    const { taskList } = formData;
    // 将taskLength区分为每日完成任务上限&活动内完成任务上线
    taskInfo = {
      ...taskInfo,
      totalLimit: taskInfo.taskLength,
      dailyLimit: taskInfo.dailyLimit || taskInfo.taskLength,
    };
    // 获取表格中是否已经存在同类型任务
    const result = taskList.filter((e): boolean => e.taskType === taskInfo.taskType);
    // 任务类型4可以重复添加，其他任务类型需要去重
    if (taskInfo.taskType === 4 || (!result.length && target === null)) {
      taskList.push(taskInfo);
    } else if (target !== null) {
      // 编辑任务
      taskList[target] = taskInfo;
    } else {
      LzMsg.error('已添加当前任务类型，请勿重新添加');
    }
    setData({ taskList });
    setVisible(false);
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="做任务获取中奖码">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item label="任务获取" required>
            <RadioGroup
              disabled
              value={formData.isShowTask}
              onChange={(isShowTask: number) => {
                if (isShowTask === 2) {
                  formData.taskList = [];
                }
                formData.isShowTask = isShowTask;
                setData(formData);
              }}
            >
              <Radio id="2" value={2}>
                不设置
              </Radio>
              <Radio id="1" value={1}>
                设置
              </Radio>
            </RadioGroup>
          </Form.Item>
          {formData.isShowTask === 1 && (
            <Form.Item label=" " colon={false}>
              <Button
                type="primary"
                onClick={(): void => {
                  setTarget(null);
                  setEditValue(null);
                  setVisible(true);
                }}
                disabled={formData.taskList.length >= 13 || activityEditDisabled()}
              >
                <Icon type="add" />
                添加任务{`${formData.taskList.length}/13`}
              </Button>
              {formData.taskList.length > 0 && (
                <Table dataSource={formData.taskList} style={{ marginTop: '15px' }}>
                  <Table.Column title="任务" cell={(_, index: number) => <div>{`任务${index + 1}`}</div>} />
                  <Table.Column title="任务标题" cell={(val, index: number, _) => TASK_TYPE[_.taskType]} />
                  <Table.Column title="任务内容" cell={(val, index: number, _) => renderTaskContent(val, index, _)} />
                  <Table.Column
                    title="完成任务次数"
                    cell={(val, index: number, _) => renderTaskLength(val, index, _)}
                  />
                  <Table.Column title="任务奖励" cell={(val, index: number, _) => renderTaskGift(val, index, _)} />
                  <Table.Column
                    width={150}
                    title="任务时间"
                    cell={(val, index: number, data) => renderTaskDate(val, index, data)}
                  />
                  <Table.Column
                    title="操作"
                    width={150}
                    cell={(val, index: number, _) => (
                      <div>
                        <Button
                          text
                          type="primary"
                          onClick={(): void => {
                            setEditValue(formData.taskList[index]);
                            setInitValue(defaultValue.taskList[index]);
                            setTarget(index);
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        {!activityEditDisabled() && index > 6 && (
                          <Button
                            text
                            type="primary"
                            onClick={(): void => {
                              formData.taskList.splice(index, 1);
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        )}
                        {activityEditDisabled() ||
                          (formData.taskList.length > 7 && index > 7 && (
                            <Button
                              text
                              onClick={(): void => {
                                formData.taskList.splice(
                                  index - 1,
                                  1,
                                  ...formData.taskList.splice(index, 1, formData.taskList[index - 1]),
                                );
                                setData(formData);
                              }}
                            >
                              <i className={`iconfont icon-iconjiantou-35`} />
                            </Button>
                          ))}
                        {activityEditDisabled() ||
                          (formData.taskList.length > 7 && index > 6 && index !== formData.taskList.length - 1 && (
                            <Button
                              text
                              onClick={(): void => {
                                formData.taskList.splice(
                                  index,
                                  1,
                                  ...formData.taskList.splice(index + 1, 1, formData.taskList[index]),
                                );
                                setData(formData);
                              }}
                            >
                              <i className={`iconfont icon-iconjiantou-34`} />
                            </Button>
                          ))}
                      </div>
                    )}
                  />
                </Table>
              )}
            </Form.Item>
          )}
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        className="lz-dialog-small"
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
      >
        <LzTask
          initValue={initValue}
          typeList={[4, 13, 14, 28, 30, 29, 7, 8]}
          editValue={editValue}
          formData={formData}
          onCancel={() => setVisible(false)}
          onSubmit={handleSubmit}
        />
      </LzDialog>
    </div>
  );
};
