import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Radio } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';
import LzColorPicker from '@/components/LzColorPicker';

const RadioGroup = Radio.Group;
const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 6,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel
        title="活动主页"
        subTitle="此页面展示活动主页的主图和背景颜色，以及活动规则、我的订单、领取攻略的图标和礼品介绍图"
      >
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="活动主图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.actBg}
                  onChange={(actBg) => {
                    setForm({ actBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、推荐图片高度480/790/1334px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ actBg: defaultValue?.actBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="页面背景图"> */}
          {/*   <Grid.Row> */}
          {/*     <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*       <LzImageSelector */}
          {/*         width={750} */}
          {/*         value={formData.pageBg} */}
          {/*         onChange={(pageBg) => { */}
          {/*           setForm({ pageBg }); */}
          {/*         }} */}
          {/*       /> */}
          {/*     </Form.Item> */}
          {/*     <Form.Item style={{ marginBottom: 0 }}> */}
          {/*       <div className={styles.tip}> */}
          {/*         <p>图片尺寸：宽度750px</p> */}
          {/*         <p>图片大小：不超过1M</p> */}
          {/*         <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*       </div> */}
          {/*       <div> */}
          {/*         <Button */}
          {/*           type="primary" */}
          {/*           text */}
          {/*           onClick={() => { */}
          {/*             setForm({ pageBg: defaultValue?.pageBg }); */}
          {/*           }} */}
          {/*         > */}
          {/*           重置 */}
          {/*         </Button> */}
          {/*       </div> */}
          {/*     </Form.Item> */}
          {/*   </Grid.Row> */}
          {/* </Form.Item> */}
          <Form.Item label="页面背景颜色">
            <LzColorPicker value={formData.actBgColor} onChange={(actBgColor) => setForm({ actBgColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ actBgColor: defaultValue?.actBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          {/* <Form.Item label="文字颜色">
            <LzColorPicker value={formData.shopNameColor} onChange={(shopNameColor) => setForm({ shopNameColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ shopNameColor: defaultValue?.shopNameColor });
              }}
            >
              重置
            </Button>
          </Form.Item> */}
          {/* <Form.Item label="是否展示店铺名称">
            <RadioGroup
              value={formData.disableShopName}
              onChange={(disableShopName: number) => {
                setForm({ disableShopName });
              }}
            >
              <Radio id="1" value={1}>
                是
              </Radio>
              <Radio id="0" value={0}>
                否
              </Radio>
            </RadioGroup>
          </Form.Item> */}
          {/* <Form.Item label="礼品介绍"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={650} */}
          {/*        height={310} */}
          {/*        value={formData.introductionImg} */}
          {/*        onChange={(introductionImg) => { */}
          {/*          setForm({ introductionImg }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：650px*310px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ introductionImg: defaultValue?.introductionImg }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
          {/* <Form.Item label="活动规则图标"> */}
          {/*   <Grid.Row> */}
          {/*     <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*       <LzImageSelector */}
          {/*         width={47} */}
          {/*         height={120} */}
          {/*         value={formData.ruleImg} */}
          {/*         onChange={(ruleImg) => { */}
          {/*           setForm({ ruleImg }); */}
          {/*         }} */}
          {/*       /> */}
          {/*     </Form.Item> */}
          {/*     <Form.Item style={{ marginBottom: 0 }}> */}
          {/*       <div className={styles.tip}> */}
          {/*         <p>图片尺寸：47px*120px</p> */}
          {/*         <p>图片大小：不超过1M</p> */}
          {/*         <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*       </div> */}
          {/*       <div> */}
          {/*         <Button */}
          {/*           type="primary" */}
          {/*           text */}
          {/*           onClick={() => { */}
          {/*             setForm({ ruleImg: defaultValue?.ruleImg }); */}
          {/*           }} */}
          {/*         > */}
          {/*           重置 */}
          {/*         </Button> */}
          {/*       </div> */}
          {/*     </Form.Item> */}
          {/*   </Grid.Row> */}
          {/* </Form.Item> */}
          {/* <Form.Item label="我的订单图标"> */}
          {/*   <Grid.Row> */}
          {/*     <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*       <LzImageSelector */}
          {/*         width={47} */}
          {/*         height={120} */}
          {/*         value={formData.recordImg} */}
          {/*         onChange={(recordImg) => { */}
          {/*           setForm({ recordImg }); */}
          {/*         }} */}
          {/*       /> */}
          {/*     </Form.Item> */}
          {/*     <Form.Item style={{ marginBottom: 0 }}> */}
          {/*       <div className={styles.tip}> */}
          {/*         <p>图片尺寸：47px*120px</p> */}
          {/*         <p>图片大小：不超过1M</p> */}
          {/*         <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*       </div> */}
          {/*       <div> */}
          {/*         <Button */}
          {/*           type="primary" */}
          {/*           text */}
          {/*           onClick={() => { */}
          {/*             setForm({ recordImg: defaultValue?.recordImg }); */}
          {/*           }} */}
          {/*         > */}
          {/*           重置 */}
          {/*         </Button> */}
          {/*       </div> */}
          {/*     </Form.Item> */}
          {/*   </Grid.Row> */}
          {/* </Form.Item> */}
          {/* <Form.Item label="领取攻略图标"> */}
          {/*   <Grid.Row> */}
          {/*     <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*       <LzImageSelector */}
          {/*         width={47} */}
          {/*         height={120} */}
          {/*         value={formData.strategyImg} */}
          {/*         onChange={(strategyImg) => { */}
          {/*           setForm({ strategyImg }); */}
          {/*         }} */}
          {/*       /> */}
          {/*     </Form.Item> */}
          {/*     <Form.Item style={{ marginBottom: 0 }}> */}
          {/*       <div className={styles.tip}> */}
          {/*         <p>图片尺寸：47px*120px</p> */}
          {/*         <p>图片大小：不超过1M</p> */}
          {/*         <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*       </div> */}
          {/*       <div> */}
          {/*         <Button */}
          {/*           type="primary" */}
          {/*           text */}
          {/*           onClick={() => { */}
          {/*             setForm({ strategyImg: defaultValue?.strategyImg }); */}
          {/*           }} */}
          {/*         > */}
          {/*           重置 */}
          {/*         </Button> */}
          {/*       </div> */}
          {/*     </Form.Item> */}
          {/*   </Grid.Row> */}
          {/* </Form.Item> */}
        </Form>
      </LzPanel>
    </div>
  );
};
